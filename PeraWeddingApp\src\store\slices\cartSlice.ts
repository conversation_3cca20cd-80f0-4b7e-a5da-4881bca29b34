import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CartState, Cart, CartItem } from '../../types';
import { openCartApi } from '../../services/api';
import { STORAGE_KEYS } from '../../constants';

// Initial state
const initialState: CartState = {
  cart: {
    items: [],
    totalItems: 0,
    subtotal: 0,
    tax: 0,
    shipping: 0,
    discount: 0,
    total: 0,
    currency: 'TL',
  },
  isLoading: false,
  isUpdating: false,
  error: null,
};

// Helper function to calculate cart totals
const calculateCartTotals = (items: CartItem[]): Partial<Cart> => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
  const tax = subtotal * 0.18; // 18% VAT in Turkey
  const shipping = subtotal > 100 ? 0 : 15; // Free shipping over 100 TL
  const total = subtotal + tax + shipping;

  return {
    totalItems,
    subtotal,
    tax,
    shipping,
    total,
  };
};

// Async thunks
export const fetchCart = createAsyncThunk(
  'cart/fetch',
  async (_, { rejectWithValue }) => {
    try {
      const response = await openCartApi.getCart();
      
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to fetch cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cart');
    }
  }
);

export const addToCart = createAsyncThunk(
  'cart/addItem',
  async (
    { productId, quantity, options }: { productId: string; quantity: number; options?: any },
    { rejectWithValue }
  ) => {
    try {
      const response = await openCartApi.addToCart(productId, quantity, options);
      
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to add item to cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add item to cart');
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateItem',
  async (
    { itemId, quantity }: { itemId: string; quantity: number },
    { rejectWithValue }
  ) => {
    try {
      const response = await openCartApi.updateCartItem(itemId, quantity);
      
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.error || 'Failed to update cart item');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update cart item');
    }
  }
);

export const removeFromCart = createAsyncThunk(
  'cart/removeItem',
  async (itemId: string, { rejectWithValue }) => {
    try {
      const response = await openCartApi.removeFromCart(itemId);
      
      if (response.success) {
        return itemId;
      } else {
        return rejectWithValue(response.error || 'Failed to remove item from cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to remove item from cart');
    }
  }
);

export const clearCart = createAsyncThunk(
  'cart/clear',
  async (_, { rejectWithValue }) => {
    try {
      const response = await openCartApi.clearCart();
      
      if (response.success) {
        return null;
      } else {
        return rejectWithValue(response.error || 'Failed to clear cart');
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to clear cart');
    }
  }
);

export const loadStoredCart = createAsyncThunk(
  'cart/loadStored',
  async (_, { rejectWithValue }) => {
    try {
      const cartData = await AsyncStorage.getItem(STORAGE_KEYS.CART_DATA);
      
      if (cartData) {
        return JSON.parse(cartData);
      }
      
      return null;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const saveCartToStorage = createAsyncThunk(
  'cart/saveToStorage',
  async (cart: Cart, { rejectWithValue }) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CART_DATA, JSON.stringify(cart));
      return cart;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// Cart slice
const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateCartTotals: (state) => {
      const totals = calculateCartTotals(state.cart.items);
      state.cart = { ...state.cart, ...totals };
    },
    addItemToLocalCart: (state, action: PayloadAction<CartItem>) => {
      const existingItemIndex = state.cart.items.findIndex(
        item => item.product.id === action.payload.product.id
      );

      if (existingItemIndex >= 0) {
        state.cart.items[existingItemIndex].quantity += action.payload.quantity;
        state.cart.items[existingItemIndex].totalPrice = 
          state.cart.items[existingItemIndex].quantity * state.cart.items[existingItemIndex].product.price;
      } else {
        state.cart.items.push(action.payload);
      }

      const totals = calculateCartTotals(state.cart.items);
      state.cart = { ...state.cart, ...totals };
    },
    removeItemFromLocalCart: (state, action: PayloadAction<string>) => {
      state.cart.items = state.cart.items.filter(item => item.id !== action.payload);
      const totals = calculateCartTotals(state.cart.items);
      state.cart = { ...state.cart, ...totals };
    },
    updateLocalCartItem: (state, action: PayloadAction<{ itemId: string; quantity: number }>) => {
      const itemIndex = state.cart.items.findIndex(item => item.id === action.payload.itemId);
      
      if (itemIndex >= 0) {
        if (action.payload.quantity <= 0) {
          state.cart.items.splice(itemIndex, 1);
        } else {
          state.cart.items[itemIndex].quantity = action.payload.quantity;
          state.cart.items[itemIndex].totalPrice = 
            action.payload.quantity * state.cart.items[itemIndex].product.price;
        }
        
        const totals = calculateCartTotals(state.cart.items);
        state.cart = { ...state.cart, ...totals };
      }
    },
    clearLocalCart: (state) => {
      state.cart = {
        items: [],
        totalItems: 0,
        subtotal: 0,
        tax: 0,
        shipping: 0,
        discount: 0,
        total: 0,
        currency: 'TL',
      };
    },
  },
  extraReducers: (builder) => {
    // Fetch cart
    builder
      .addCase(fetchCart.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.isLoading = false;
        state.cart = action.payload;
        state.error = null;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Add to cart
    builder
      .addCase(addToCart.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.cart = action.payload;
        state.error = null;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Update cart item
    builder
      .addCase(updateCartItem.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateCartItem.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.cart = action.payload;
        state.error = null;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Remove from cart
    builder
      .addCase(removeFromCart.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.cart.items = state.cart.items.filter(item => item.id !== action.payload);
        const totals = calculateCartTotals(state.cart.items);
        state.cart = { ...state.cart, ...totals };
        state.error = null;
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Clear cart
    builder
      .addCase(clearCart.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(clearCart.fulfilled, (state) => {
        state.isUpdating = false;
        state.cart = {
          items: [],
          totalItems: 0,
          subtotal: 0,
          tax: 0,
          shipping: 0,
          discount: 0,
          total: 0,
          currency: 'TL',
        };
        state.error = null;
      })
      .addCase(clearCart.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // Load stored cart
    builder
      .addCase(loadStoredCart.fulfilled, (state, action) => {
        if (action.payload) {
          state.cart = action.payload;
        }
      });
  },
});

export const {
  clearError,
  updateCartTotals,
  addItemToLocalCart,
  removeItemFromLocalCart,
  updateLocalCartItem,
  clearLocalCart,
} = cartSlice.actions;

export default cartSlice.reducer;
