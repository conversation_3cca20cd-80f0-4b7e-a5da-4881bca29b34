import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';
import tr from './tr';
import en from './en';

// Create i18n instance
const i18n = new I18n({
  tr,
  en,
});

// Set default locale
i18n.defaultLocale = 'tr';
i18n.locale = Localization.locale.startsWith('tr') ? 'tr' : 'en';

// Enable fallbacks
i18n.enableFallback = true;

// Translation function
export const t = (key: string, options?: any): string => {
  return i18n.t(key, options);
};

// Set locale function
export const setLocale = (locale: string) => {
  i18n.locale = locale;
};

// Get current locale
export const getCurrentLocale = (): string => {
  return i18n.locale;
};

// Get available locales
export const getAvailableLocales = () => {
  return [
    { code: 'tr', name: 'Türkçe', flag: '🇹🇷' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
  ];
};

export default i18n;
