# Pera Wedding App Environment Configuration
# Copy this file to .env and update with your actual values

# API Configuration
API_BASE_URL=https://www.perawedding.com.tr/api
API_TIMEOUT=10000

# PayTR Configuration
PAYTR_MERCHANT_ID=your_merchant_id_here
PAYTR_MERCHANT_KEY=your_merchant_key_here
PAYTR_MERCHANT_SALT=your_merchant_salt_here
PAYTR_TEST_MODE=true

# App Configuration
APP_NAME=Pera Wedding
APP_VERSION=1.0.0
APP_ENVIRONMENT=development

# Firebase Configuration (Optional)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# Sentry Configuration (Optional)
SENTRY_DSN=your_sentry_dsn_here

# Google Analytics (Optional)
GA_TRACKING_ID=your_ga_tracking_id

# Social Media Links
FACEBOOK_URL=https://facebook.com/perawedding
INSTAGRAM_URL=https://instagram.com/perawedding
TWITTER_URL=https://twitter.com/perawedding

# Support Information
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE=+90_XXX_XXX_XX_XX
COMPANY_ADDRESS=Your Company Address Here

# Legal URLs
PRIVACY_POLICY_URL=https://www.perawedding.com.tr/privacy-policy
TERMS_OF_SERVICE_URL=https://www.perawedding.com.tr/terms-of-service
RETURN_POLICY_URL=https://www.perawedding.com.tr/return-policy

# Development Settings
DEBUG_MODE=true
LOG_LEVEL=debug
ENABLE_FLIPPER=true

# Build Configuration
ANDROID_PACKAGE_NAME=com.perawedding.app
IOS_BUNDLE_IDENTIFIER=com.perawedding.app
APP_SCHEME=perawedding
