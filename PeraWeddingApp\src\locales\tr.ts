export default {
  // Common
  common: {
    loading: 'Yükleniyor...',
    error: '<PERSON><PERSON>',
    success: '<PERSON><PERSON><PERSON><PERSON>l<PERSON>',
    cancel: '<PERSON>pta<PERSON>',
    confirm: '<PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    delete: '<PERSON><PERSON>',
    edit: '<PERSON><PERSON><PERSON><PERSON>',
    add: '<PERSON><PERSON>',
    remove: '<PERSON><PERSON><PERSON><PERSON>',
    search: '<PERSON>',
    filter: '<PERSON><PERSON><PERSON><PERSON>',
    sort: '<PERSON><PERSON><PERSON><PERSON>',
    clear: '<PERSON><PERSON><PERSON>',
    apply: '<PERSON><PERSON><PERSON><PERSON>',
    close: '<PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON>',
    next: '<PERSON><PERSON><PERSON>',
    previous: '<PERSON><PERSON><PERSON>',
    continue: '<PERSON><PERSON>t',
    retry: 'Te<PERSON><PERSON> Dene',
    refresh: 'Yenile',
    share: '<PERSON><PERSON><PERSON>',
    copy: '<PERSON><PERSON><PERSON>',
    paste: 'Yapıştır',
    select: 'Se<PERSON>',
    selectAll: 'Tü<PERSON>ün<PERSON>',
    none: 'Hiçbiri',
    all: 'Tümü',
    yes: 'Evet',
    no: 'Hayır',
    ok: 'Tamam',
    done: '<PERSON><PERSON>',
    skip: '<PERSON>e<PERSON>',
    optional: 'Opsiyonel',
    required: '<PERSON><PERSON><PERSON><PERSON>',
    currency: 'TL',
  },

  // Navigation
  navigation: {
    home: '<PERSON>',
    categories: '<PERSON><PERSON><PERSON>',
    search: '<PERSON><PERSON>',
    cart: 'Sepeti<PERSON>',
    profile: 'Profil',
    login: '<PERSON><PERSON><PERSON>p',
    register: 'Üye Ol',
    logout: 'Çıkış Yap',
    settings: 'Ayarlar',
    about: 'Hakkımızda',
    contact: 'İletişim',
    help: 'Yardım',
    wishlist: 'Favorilerim',
    orders: 'Siparişlerim',
    addresses: 'Adreslerim',
  },

  // Authentication
  auth: {
    email: 'E-posta',
    password: 'Şifre',
    confirmPassword: 'Şifre Tekrarı',
    firstName: 'Ad',
    lastName: 'Soyad',
    phone: 'Telefon',
    rememberMe: 'Beni Hatırla',
    forgotPassword: 'Şifremi Unuttum',
    dontHaveAccount: 'Hesabınız yok mu?',
    alreadyHaveAccount: 'Zaten hesabınız var mı?',
    createAccount: 'Hesap Oluştur',
    signIn: 'Giriş Yap',
    signUp: 'Üye Ol',
    signOut: 'Çıkış Yap',
    resetPassword: 'Şifre Sıfırla',
    acceptTerms: 'Kullanım koşullarını kabul ediyorum',
    loginSuccess: 'Başarıyla giriş yapıldı',
    registerSuccess: 'Hesabınız başarıyla oluşturuldu',
    logoutSuccess: 'Başarıyla çıkış yapıldı',
    invalidCredentials: 'Geçersiz e-posta veya şifre',
    emailRequired: 'E-posta adresi gerekli',
    passwordRequired: 'Şifre gerekli',
    passwordTooShort: 'Şifre en az 6 karakter olmalıdır',
    passwordsNotMatch: 'Şifreler eşleşmiyor',
    invalidEmail: 'Geçersiz e-posta adresi',
    termsRequired: 'Kullanım koşullarını kabul etmelisiniz',
  },

  // Products
  products: {
    products: 'Ürünler',
    product: 'Ürün',
    featuredProducts: 'Öne Çıkan Ürünler',
    newProducts: 'Yeni Ürünler',
    bestSellers: 'En Çok Satanlar',
    onSale: 'İndirimli Ürünler',
    outOfStock: 'Stokta Yok',
    inStock: 'Stokta Var',
    addToCart: 'Sepete Ekle',
    addToWishlist: 'Favorilere Ekle',
    removeFromWishlist: 'Favorilerden Çıkar',
    quickView: 'Hızlı Görünüm',
    viewDetails: 'Detayları Gör',
    price: 'Fiyat',
    originalPrice: 'Eski Fiyat',
    discount: 'İndirim',
    rating: 'Değerlendirme',
    reviews: 'Yorumlar',
    description: 'Açıklama',
    specifications: 'Özellikler',
    images: 'Resimler',
    options: 'Seçenekler',
    quantity: 'Adet',
    sku: 'Ürün Kodu',
    brand: 'Marka',
    category: 'Kategori',
    tags: 'Etiketler',
    relatedProducts: 'İlgili Ürünler',
    similarProducts: 'Benzer Ürünler',
    productAddedToCart: 'Ürün sepete eklendi',
    productAddedToWishlist: 'Ürün favorilere eklendi',
    productRemovedFromWishlist: 'Ürün favorilerden çıkarıldı',
    selectOptions: 'Seçenekleri belirleyin',
    noProductsFound: 'Ürün bulunamadı',
    loadingProducts: 'Ürünler yükleniyor...',
  },

  // Cart
  cart: {
    cart: 'Sepet',
    myCart: 'Sepetim',
    emptyCart: 'Sepetiniz boş',
    cartTotal: 'Sepet Toplamı',
    subtotal: 'Ara Toplam',
    tax: 'KDV',
    shipping: 'Kargo',
    discount: 'İndirim',
    total: 'Toplam',
    proceedToCheckout: 'Ödemeye Geç',
    continueShopping: 'Alışverişe Devam Et',
    updateCart: 'Sepeti Güncelle',
    removeItem: 'Ürünü Çıkar',
    clearCart: 'Sepeti Temizle',
    itemsInCart: 'sepette ürün',
    freeShipping: 'Ücretsiz Kargo',
    freeShippingThreshold: 'TL üzeri alışverişlerde ücretsiz kargo',
    cartUpdated: 'Sepet güncellendi',
    itemRemoved: 'Ürün sepetten çıkarıldı',
    cartCleared: 'Sepet temizlendi',
  },

  // Categories
  categories: {
    categories: 'Kategoriler',
    allCategories: 'Tüm Kategoriler',
    weddingMemoryBooks: 'Düğün Anı Defteri',
    babyMemoryBooks: 'Bebek Anı Defteri',
    digitalInvitations: 'Dijital Davetiyeler',
    weddingPlanner: 'Düğün Planlayıcı',
    noCategories: 'Kategori bulunamadı',
    loadingCategories: 'Kategoriler yükleniyor...',
  },

  // Orders
  orders: {
    orders: 'Siparişler',
    myOrders: 'Siparişlerim',
    orderHistory: 'Sipariş Geçmişi',
    orderDetail: 'Sipariş Detayı',
    orderNumber: 'Sipariş Numarası',
    orderDate: 'Sipariş Tarihi',
    orderStatus: 'Sipariş Durumu',
    orderTotal: 'Sipariş Toplamı',
    trackingNumber: 'Takip Numarası',
    shippingAddress: 'Teslimat Adresi',
    billingAddress: 'Fatura Adresi',
    paymentMethod: 'Ödeme Yöntemi',
    orderItems: 'Sipariş Ürünleri',
    noOrders: 'Henüz siparişiniz bulunmuyor',
    orderPlaced: 'Sipariş verildi',
    orderConfirmed: 'Sipariş onaylandı',
    orderProcessing: 'Sipariş hazırlanıyor',
    orderShipped: 'Sipariş kargoya verildi',
    orderDelivered: 'Sipariş teslim edildi',
    orderCancelled: 'Sipariş iptal edildi',
    reorder: 'Tekrar Sipariş Ver',
    cancelOrder: 'Siparişi İptal Et',
    trackOrder: 'Siparişi Takip Et',
  },

  // Profile
  profile: {
    profile: 'Profil',
    myProfile: 'Profilim',
    editProfile: 'Profili Düzenle',
    personalInfo: 'Kişisel Bilgiler',
    contactInfo: 'İletişim Bilgileri',
    accountSettings: 'Hesap Ayarları',
    changePassword: 'Şifre Değiştir',
    currentPassword: 'Mevcut Şifre',
    newPassword: 'Yeni Şifre',
    confirmNewPassword: 'Yeni Şifre Tekrarı',
    profileUpdated: 'Profil güncellendi',
    passwordChanged: 'Şifre değiştirildi',
    deleteAccount: 'Hesabı Sil',
    deleteAccountConfirm: 'Hesabınızı silmek istediğinizden emin misiniz?',
  },

  // Search
  search: {
    search: 'Arama',
    searchProducts: 'Ürün Ara',
    searchResults: 'Arama Sonuçları',
    noResults: 'Sonuç bulunamadı',
    searchPlaceholder: 'Ürün, kategori veya marka ara...',
    recentSearches: 'Son Aramalar',
    popularSearches: 'Popüler Aramalar',
    clearSearchHistory: 'Arama Geçmişini Temizle',
    resultsFound: 'sonuç bulundu',
  },

  // Errors
  errors: {
    networkError: 'İnternet bağlantısı hatası',
    serverError: 'Sunucu hatası',
    unknownError: 'Bilinmeyen hata',
    tryAgain: 'Tekrar deneyin',
    somethingWentWrong: 'Bir şeyler ters gitti',
    pageNotFound: 'Sayfa bulunamadı',
    accessDenied: 'Erişim reddedildi',
    sessionExpired: 'Oturum süresi doldu',
    invalidRequest: 'Geçersiz istek',
    validationError: 'Doğrulama hatası',
  },

  // Success Messages
  success: {
    operationCompleted: 'İşlem başarıyla tamamlandı',
    dataSaved: 'Veriler kaydedildi',
    emailSent: 'E-posta gönderildi',
    passwordReset: 'Şifre sıfırlama bağlantısı gönderildi',
    subscribed: 'Abone oldunuz',
    unsubscribed: 'Abonelikten çıktınız',
  },

  // Validation
  validation: {
    required: 'Bu alan zorunludur',
    invalidEmail: 'Geçersiz e-posta adresi',
    invalidPhone: 'Geçersiz telefon numarası',
    passwordTooShort: 'Şifre en az 6 karakter olmalıdır',
    passwordsNotMatch: 'Şifreler eşleşmiyor',
    invalidFormat: 'Geçersiz format',
    tooShort: 'Çok kısa',
    tooLong: 'Çok uzun',
    invalidCharacters: 'Geçersiz karakterler',
  },

  // Payment
  payment: {
    payment: 'Ödeme',
    paymentMethod: 'Ödeme Yöntemi',
    paymentInfo: 'Ödeme Bilgileri',
    paymentSuccess: 'Ödeme başarılı',
    paymentFailed: 'Ödeme başarısız',
    paymentPending: 'Ödeme bekleniyor',
    paymentCancelled: 'Ödeme iptal edildi',
    payWithPayTR: 'PayTR ile Öde',
    securePayment: 'Güvenli Ödeme',
    paymentProcessing: 'Ödeme işleniyor...',
    redirectingToPayment: 'Ödeme sayfasına yönlendiriliyor...',
  },

  // Shipping
  shipping: {
    shipping: 'Kargo',
    shippingAddress: 'Teslimat Adresi',
    billingAddress: 'Fatura Adresi',
    freeShipping: 'Ücretsiz Kargo',
    standardShipping: 'Standart Kargo',
    expressShipping: 'Hızlı Kargo',
    shippingCost: 'Kargo Ücreti',
    estimatedDelivery: 'Tahmini Teslimat',
    trackingNumber: 'Takip Numarası',
    trackShipment: 'Kargoyu Takip Et',
  },

  // Contact
  contact: {
    contact: 'İletişim',
    contactUs: 'Bize Ulaşın',
    name: 'Ad Soyad',
    subject: 'Konu',
    message: 'Mesaj',
    sendMessage: 'Mesaj Gönder',
    messageSent: 'Mesajınız gönderildi',
    phone: 'Telefon',
    email: 'E-posta',
    address: 'Adres',
    workingHours: 'Çalışma Saatleri',
    socialMedia: 'Sosyal Medya',
  },

  // About
  about: {
    aboutUs: 'Hakkımızda',
    ourStory: 'Hikayemiz',
    ourMission: 'Misyonumuz',
    ourVision: 'Vizyonumuz',
    ourValues: 'Değerlerimiz',
    ourTeam: 'Ekibimiz',
    careers: 'Kariyer',
    press: 'Basın',
    investors: 'Yatırımcılar',
  },

  // Settings
  settings: {
    settings: 'Ayarlar',
    language: 'Dil',
    notifications: 'Bildirimler',
    privacy: 'Gizlilik',
    security: 'Güvenlik',
    theme: 'Tema',
    fontSize: 'Yazı Boyutu',
    currency: 'Para Birimi',
    country: 'Ülke',
    timeZone: 'Saat Dilimi',
    autoUpdate: 'Otomatik Güncelleme',
    dataUsage: 'Veri Kullanımı',
    storage: 'Depolama',
    cache: 'Önbellek',
    clearCache: 'Önbelleği Temizle',
    resetSettings: 'Ayarları Sıfırla',
  },

  // Notifications
  notifications: {
    notifications: 'Bildirimler',
    pushNotifications: 'Anlık Bildirimler',
    emailNotifications: 'E-posta Bildirimleri',
    smsNotifications: 'SMS Bildirimleri',
    orderUpdates: 'Sipariş Güncellemeleri',
    promotions: 'Promosyonlar',
    newsletter: 'Haber Bülteni',
    newProducts: 'Yeni Ürünler',
    priceDrops: 'Fiyat Düşüşleri',
    backInStock: 'Stoka Geri Dönüş',
    markAsRead: 'Okundu Olarak İşaretle',
    markAllAsRead: 'Tümünü Okundu Olarak İşaretle',
    clearAll: 'Tümünü Temizle',
    noNotifications: 'Bildirim bulunmuyor',
  },

  // Addresses
  addresses: {
    addresses: 'Adresler',
    addAddress: 'Adres Ekle',
    editAddress: 'Adresi Düzenle',
    deleteAddress: 'Adresi Sil',
    defaultAddress: 'Varsayılan Adres',
    setAsDefault: 'Varsayılan Yap',
    addressTitle: 'Adres Başlığı',
    fullName: 'Ad Soyad',
    company: 'Şirket',
    addressLine1: 'Adres Satırı 1',
    addressLine2: 'Adres Satırı 2',
    city: 'Şehir',
    state: 'İl/Bölge',
    postalCode: 'Posta Kodu',
    country: 'Ülke',
    addressSaved: 'Adres kaydedildi',
    addressDeleted: 'Adres silindi',
    confirmDeleteAddress: 'Bu adresi silmek istediğinizden emin misiniz?',
  },
};
