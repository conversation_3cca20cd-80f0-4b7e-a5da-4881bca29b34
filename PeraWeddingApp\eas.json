{"cli": {"version": ">= 3.0.0"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}, "ios": {"simulator": true}}, "production": {"android": {"buildType": "app-bundle"}, "ios": {"buildConfiguration": "Release"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}, "ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}}}}