# Pera Wedding App - Deployment Guide

This guide provides step-by-step instructions for deploying the Pera Wedding React Native app to production.

## Pre-Deployment Checklist

### 1. Code Review and Testing
- [ ] All features implemented and tested
- [ ] Unit tests passing
- [ ] Integration tests completed
- [ ] Performance testing done
- [ ] Security review completed
- [ ] Code review approved

### 2. Configuration
- [ ] Production API endpoints configured
- [ ] PayTR production credentials set
- [ ] App icons and splash screens added
- [ ] App metadata updated (name, version, description)
- [ ] Privacy policy and terms of service links added
- [ ] Analytics and crash reporting configured

### 3. Assets and Content
- [ ] All images optimized for mobile
- [ ] App store screenshots prepared
- [ ] App store descriptions written (Turkish and English)
- [ ] Keywords for ASO (App Store Optimization) researched

## Android Deployment (Google Play Store)

### Step 1: Prepare Android Build

1. **Update app.json**
   ```json
   {
     "expo": {
       "name": "Pera Wedding",
       "slug": "pera-wedding-app",
       "version": "1.0.0",
       "android": {
         "package": "com.perawedding.app",
         "versionCode": 1,
         "permissions": [
           "INTERNET",
           "CAMERA",
           "READ_EXTERNAL_STORAGE",
           "WRITE_EXTERNAL_STORAGE"
         ]
       }
     }
   }
   ```

2. **Generate Android Keystore**
   ```bash
   keytool -genkeypair -v -keystore pera-wedding-release.keystore -alias pera-wedding -keyalg RSA -keysize 2048 -validity 10000
   ```

3. **Configure EAS Build**
   ```bash
   npm install -g @expo/cli
   eas build:configure
   ```

   Update `eas.json`:
   ```json
   {
     "build": {
       "production": {
         "android": {
           "buildType": "apk",
           "gradleCommand": ":app:assembleRelease"
         }
       }
     }
   }
   ```

### Step 2: Build APK

```bash
# Build production APK
eas build --platform android --profile production

# Or build AAB for Play Store
eas build --platform android --profile production --build-type app-bundle
```

### Step 3: Google Play Console Setup

1. **Create Developer Account**
   - Go to [Google Play Console](https://play.google.com/console)
   - Pay $25 registration fee
   - Complete account verification

2. **Create New App**
   - Click "Create app"
   - Fill in app details:
     - App name: "Pera Wedding"
     - Default language: Turkish
     - App type: App
     - Category: Shopping

3. **Upload APK/AAB**
   - Go to "Release" > "Production"
   - Upload your APK or AAB file
   - Fill in release notes

4. **Store Listing**
   - App name: "Pera Wedding - Düğün Anı Defteri"
   - Short description: "Düğün anılarınızı ölümsüzleştirin"
   - Full description: [Detailed app description]
   - Screenshots: Add 2-8 screenshots
   - Feature graphic: 1024x500 image
   - App icon: 512x512 PNG

5. **Content Rating**
   - Complete content rating questionnaire
   - Target audience: Everyone

6. **App Content**
   - Privacy policy URL
   - Target audience and content
   - News apps declaration (if applicable)

### Step 4: Release

1. **Review and Publish**
   - Review all sections for completeness
   - Submit for review
   - Wait for Google approval (usually 1-3 days)

## iOS Deployment (App Store)

### Step 1: Apple Developer Account

1. **Enroll in Apple Developer Program**
   - Go to [Apple Developer](https://developer.apple.com)
   - Pay $99 annual fee
   - Complete enrollment

2. **Create App ID**
   - Go to Certificates, Identifiers & Profiles
   - Create new App ID: `com.perawedding.app`
   - Enable required capabilities

### Step 2: Prepare iOS Build

1. **Update app.json**
   ```json
   {
     "expo": {
       "ios": {
         "bundleIdentifier": "com.perawedding.app",
         "buildNumber": "1",
         "supportsTablet": true,
         "infoPlist": {
           "NSCameraUsageDescription": "This app uses camera to take photos for profile",
           "NSPhotoLibraryUsageDescription": "This app accesses photo library to select images"
         }
       }
     }
   }
   ```

2. **Build for iOS**
   ```bash
   eas build --platform ios --profile production
   ```

### Step 3: App Store Connect

1. **Create App Record**
   - Go to [App Store Connect](https://appstoreconnect.apple.com)
   - Click "My Apps" > "+"
   - Fill in app information:
     - Name: "Pera Wedding"
     - Bundle ID: com.perawedding.app
     - SKU: pera-wedding-app

2. **App Information**
   - Subtitle: "Düğün Anı Defteri"
   - Category: Shopping
   - Content Rights: No
   - Age Rating: 4+

3. **Pricing and Availability**
   - Price: Free
   - Availability: All countries

4. **App Store Information**
   - Name: "Pera Wedding - Düğün Anı Defteri"
   - Description: [Detailed app description]
   - Keywords: düğün, anı defteri, wedding, memory book
   - Screenshots: 6.5", 5.5", 12.9" iPad Pro
   - App Preview: Optional video

### Step 4: Upload Build

1. **Upload via Xcode or Transporter**
   - Download build from EAS
   - Upload to App Store Connect

2. **TestFlight (Optional)**
   - Add internal testers
   - Test app functionality
   - Gather feedback

3. **Submit for Review**
   - Select build version
   - Add release notes
   - Submit for App Store review
   - Wait for Apple approval (usually 1-7 days)

## PayTR Production Configuration

### Step 1: PayTR Account Setup

1. **Create Production Account**
   - Go to [PayTR](https://www.paytr.com)
   - Complete business verification
   - Get production credentials

2. **Configure Webhooks**
   - Success URL: `https://your-backend.com/paytr/success`
   - Fail URL: `https://your-backend.com/paytr/fail`
   - IPN URL: `https://your-backend.com/paytr/ipn`

### Step 2: Update App Configuration

```typescript
// src/constants/index.ts
export const PAYTR_CONFIG = {
  MERCHANT_ID: 'your_production_merchant_id',
  MERCHANT_KEY: 'your_production_merchant_key',
  MERCHANT_SALT: 'your_production_merchant_salt',
  TEST_MODE: false, // Set to false for production
  CURRENCY: 'TL',
  LANG: 'tr',
};
```

## Backend API Configuration

### OpenCart API Setup

1. **Install REST API Extension**
   - Download OpenCart REST API extension
   - Install and configure in admin panel

2. **API User Configuration**
   - Create API user in OpenCart admin
   - Generate API key
   - Set appropriate permissions

3. **CORS Configuration**
   ```php
   // Add to .htaccess or server configuration
   Header always set Access-Control-Allow-Origin "*"
   Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
   Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
   ```

### Required API Endpoints

Ensure these endpoints are available:

- `GET /api/products` - Get products list
- `GET /api/products/{id}` - Get product details
- `GET /api/categories` - Get categories
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/cart` - Get cart contents
- `POST /api/cart/add` - Add item to cart
- `POST /api/orders` - Create order
- `GET /api/orders` - Get user orders

## Monitoring and Analytics

### Crash Reporting

1. **Sentry Integration**
   ```bash
   npm install @sentry/react-native
   ```

2. **Configure Sentry**
   ```typescript
   import * as Sentry from '@sentry/react-native';
   
   Sentry.init({
     dsn: 'your-sentry-dsn',
   });
   ```

### Analytics

1. **Firebase Analytics**
   ```bash
   expo install @react-native-firebase/analytics
   ```

2. **Google Analytics**
   - Set up GA4 property
   - Track key events (purchases, sign-ups, etc.)

## Post-Deployment

### 1. Monitor App Performance
- Check crash reports daily
- Monitor user feedback and ratings
- Track key metrics (downloads, retention, revenue)

### 2. User Support
- Set up customer support channels
- Create FAQ section
- Monitor app store reviews and respond

### 3. Updates and Maintenance
- Plan regular updates
- Monitor for security vulnerabilities
- Keep dependencies updated
- A/B test new features

### 4. Marketing
- App Store Optimization (ASO)
- Social media promotion
- Email marketing to existing customers
- Influencer partnerships

## Rollback Plan

In case of critical issues:

1. **Immediate Actions**
   - Remove app from stores if necessary
   - Communicate with users via push notifications
   - Prepare hotfix release

2. **Rollback Process**
   - Revert to previous stable version
   - Update API endpoints if needed
   - Test thoroughly before re-release

## Security Considerations

1. **API Security**
   - Use HTTPS only
   - Implement rate limiting
   - Validate all inputs
   - Use proper authentication

2. **App Security**
   - Obfuscate sensitive code
   - Use certificate pinning
   - Implement root/jailbreak detection
   - Secure local storage

3. **Payment Security**
   - Never store payment details locally
   - Use PayTR's secure payment flow
   - Implement proper error handling
   - Log security events

## Support and Maintenance

- **Regular Updates**: Plan monthly updates
- **Security Patches**: Apply immediately when available
- **Performance Monitoring**: Use tools like Firebase Performance
- **User Feedback**: Implement in-app feedback system
- **Documentation**: Keep deployment docs updated

This deployment guide should be followed carefully to ensure a successful launch of the Pera Wedding mobile app.
