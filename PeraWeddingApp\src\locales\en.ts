export default {
  // Common
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    remove: 'Remove',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    clear: 'Clear',
    apply: 'Apply',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    continue: 'Continue',
    retry: 'Retry',
    refresh: 'Refresh',
    share: 'Share',
    copy: 'Copy',
    paste: 'Paste',
    select: 'Select',
    selectAll: 'Select All',
    none: 'None',
    all: 'All',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    done: 'Done',
    skip: 'Skip',
    optional: 'Optional',
    required: 'Required',
    currency: 'TL',
  },

  // Navigation
  navigation: {
    home: 'Home',
    categories: 'Categories',
    search: 'Search',
    cart: 'Cart',
    profile: 'Profile',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    settings: 'Settings',
    about: 'About',
    contact: 'Contact',
    help: 'Help',
    wishlist: 'Wishlist',
    orders: 'Orders',
    addresses: 'Addresses',
  },

  // Authentication
  auth: {
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    firstName: 'First Name',
    lastName: 'Last Name',
    phone: 'Phone',
    rememberMe: 'Remember Me',
    forgotPassword: 'Forgot Password',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',
    createAccount: 'Create Account',
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
    resetPassword: 'Reset Password',
    acceptTerms: 'I accept the terms and conditions',
    loginSuccess: 'Successfully logged in',
    registerSuccess: 'Account created successfully',
    logoutSuccess: 'Successfully logged out',
    invalidCredentials: 'Invalid email or password',
    emailRequired: 'Email is required',
    passwordRequired: 'Password is required',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordsNotMatch: 'Passwords do not match',
    invalidEmail: 'Invalid email address',
    termsRequired: 'You must accept the terms and conditions',
  },

  // Products
  products: {
    products: 'Products',
    product: 'Product',
    featuredProducts: 'Featured Products',
    newProducts: 'New Products',
    bestSellers: 'Best Sellers',
    onSale: 'On Sale',
    outOfStock: 'Out of Stock',
    inStock: 'In Stock',
    addToCart: 'Add to Cart',
    addToWishlist: 'Add to Wishlist',
    removeFromWishlist: 'Remove from Wishlist',
    quickView: 'Quick View',
    viewDetails: 'View Details',
    price: 'Price',
    originalPrice: 'Original Price',
    discount: 'Discount',
    rating: 'Rating',
    reviews: 'Reviews',
    description: 'Description',
    specifications: 'Specifications',
    images: 'Images',
    options: 'Options',
    quantity: 'Quantity',
    sku: 'SKU',
    brand: 'Brand',
    category: 'Category',
    tags: 'Tags',
    relatedProducts: 'Related Products',
    similarProducts: 'Similar Products',
    productAddedToCart: 'Product added to cart',
    productAddedToWishlist: 'Product added to wishlist',
    productRemovedFromWishlist: 'Product removed from wishlist',
    selectOptions: 'Please select options',
    noProductsFound: 'No products found',
    loadingProducts: 'Loading products...',
  },

  // Cart
  cart: {
    cart: 'Cart',
    myCart: 'My Cart',
    emptyCart: 'Your cart is empty',
    cartTotal: 'Cart Total',
    subtotal: 'Subtotal',
    tax: 'Tax',
    shipping: 'Shipping',
    discount: 'Discount',
    total: 'Total',
    proceedToCheckout: 'Proceed to Checkout',
    continueShopping: 'Continue Shopping',
    updateCart: 'Update Cart',
    removeItem: 'Remove Item',
    clearCart: 'Clear Cart',
    itemsInCart: 'items in cart',
    freeShipping: 'Free Shipping',
    freeShippingThreshold: 'Free shipping on orders over',
    cartUpdated: 'Cart updated',
    itemRemoved: 'Item removed from cart',
    cartCleared: 'Cart cleared',
  },

  // Categories
  categories: {
    categories: 'Categories',
    allCategories: 'All Categories',
    weddingMemoryBooks: 'Wedding Memory Books',
    babyMemoryBooks: 'Baby Memory Books',
    digitalInvitations: 'Digital Invitations',
    weddingPlanner: 'Wedding Planner',
    noCategories: 'No categories found',
    loadingCategories: 'Loading categories...',
  },

  // Orders
  orders: {
    orders: 'Orders',
    myOrders: 'My Orders',
    orderHistory: 'Order History',
    orderDetail: 'Order Detail',
    orderNumber: 'Order Number',
    orderDate: 'Order Date',
    orderStatus: 'Order Status',
    orderTotal: 'Order Total',
    trackingNumber: 'Tracking Number',
    shippingAddress: 'Shipping Address',
    billingAddress: 'Billing Address',
    paymentMethod: 'Payment Method',
    orderItems: 'Order Items',
    noOrders: 'You have no orders yet',
    orderPlaced: 'Order placed',
    orderConfirmed: 'Order confirmed',
    orderProcessing: 'Order processing',
    orderShipped: 'Order shipped',
    orderDelivered: 'Order delivered',
    orderCancelled: 'Order cancelled',
    reorder: 'Reorder',
    cancelOrder: 'Cancel Order',
    trackOrder: 'Track Order',
  },

  // Profile
  profile: {
    profile: 'Profile',
    myProfile: 'My Profile',
    editProfile: 'Edit Profile',
    personalInfo: 'Personal Information',
    contactInfo: 'Contact Information',
    accountSettings: 'Account Settings',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    profileUpdated: 'Profile updated',
    passwordChanged: 'Password changed',
    deleteAccount: 'Delete Account',
    deleteAccountConfirm: 'Are you sure you want to delete your account?',
  },

  // Search
  search: {
    search: 'Search',
    searchProducts: 'Search Products',
    searchResults: 'Search Results',
    noResults: 'No results found',
    searchPlaceholder: 'Search products, categories or brands...',
    recentSearches: 'Recent Searches',
    popularSearches: 'Popular Searches',
    clearSearchHistory: 'Clear Search History',
    resultsFound: 'results found',
  },

  // Errors
  errors: {
    networkError: 'Network connection error',
    serverError: 'Server error',
    unknownError: 'Unknown error',
    tryAgain: 'Try again',
    somethingWentWrong: 'Something went wrong',
    pageNotFound: 'Page not found',
    accessDenied: 'Access denied',
    sessionExpired: 'Session expired',
    invalidRequest: 'Invalid request',
    validationError: 'Validation error',
  },

  // Success Messages
  success: {
    operationCompleted: 'Operation completed successfully',
    dataSaved: 'Data saved',
    emailSent: 'Email sent',
    passwordReset: 'Password reset link sent',
    subscribed: 'Subscribed successfully',
    unsubscribed: 'Unsubscribed successfully',
  },

  // Validation
  validation: {
    required: 'This field is required',
    invalidEmail: 'Invalid email address',
    invalidPhone: 'Invalid phone number',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordsNotMatch: 'Passwords do not match',
    invalidFormat: 'Invalid format',
    tooShort: 'Too short',
    tooLong: 'Too long',
    invalidCharacters: 'Invalid characters',
  },

  // Payment
  payment: {
    payment: 'Payment',
    paymentMethod: 'Payment Method',
    paymentInfo: 'Payment Information',
    paymentSuccess: 'Payment successful',
    paymentFailed: 'Payment failed',
    paymentPending: 'Payment pending',
    paymentCancelled: 'Payment cancelled',
    payWithPayTR: 'Pay with PayTR',
    securePayment: 'Secure Payment',
    paymentProcessing: 'Processing payment...',
    redirectingToPayment: 'Redirecting to payment...',
  },

  // Shipping
  shipping: {
    shipping: 'Shipping',
    shippingAddress: 'Shipping Address',
    billingAddress: 'Billing Address',
    freeShipping: 'Free Shipping',
    standardShipping: 'Standard Shipping',
    expressShipping: 'Express Shipping',
    shippingCost: 'Shipping Cost',
    estimatedDelivery: 'Estimated Delivery',
    trackingNumber: 'Tracking Number',
    trackShipment: 'Track Shipment',
  },

  // Contact
  contact: {
    contact: 'Contact',
    contactUs: 'Contact Us',
    name: 'Full Name',
    subject: 'Subject',
    message: 'Message',
    sendMessage: 'Send Message',
    messageSent: 'Your message has been sent',
    phone: 'Phone',
    email: 'Email',
    address: 'Address',
    workingHours: 'Working Hours',
    socialMedia: 'Social Media',
  },

  // About
  about: {
    aboutUs: 'About Us',
    ourStory: 'Our Story',
    ourMission: 'Our Mission',
    ourVision: 'Our Vision',
    ourValues: 'Our Values',
    ourTeam: 'Our Team',
    careers: 'Careers',
    press: 'Press',
    investors: 'Investors',
  },

  // Settings
  settings: {
    settings: 'Settings',
    language: 'Language',
    notifications: 'Notifications',
    privacy: 'Privacy',
    security: 'Security',
    theme: 'Theme',
    fontSize: 'Font Size',
    currency: 'Currency',
    country: 'Country',
    timeZone: 'Time Zone',
    autoUpdate: 'Auto Update',
    dataUsage: 'Data Usage',
    storage: 'Storage',
    cache: 'Cache',
    clearCache: 'Clear Cache',
    resetSettings: 'Reset Settings',
  },

  // Notifications
  notifications: {
    notifications: 'Notifications',
    pushNotifications: 'Push Notifications',
    emailNotifications: 'Email Notifications',
    smsNotifications: 'SMS Notifications',
    orderUpdates: 'Order Updates',
    promotions: 'Promotions',
    newsletter: 'Newsletter',
    newProducts: 'New Products',
    priceDrops: 'Price Drops',
    backInStock: 'Back in Stock',
    markAsRead: 'Mark as Read',
    markAllAsRead: 'Mark All as Read',
    clearAll: 'Clear All',
    noNotifications: 'No notifications',
  },

  // Addresses
  addresses: {
    addresses: 'Addresses',
    addAddress: 'Add Address',
    editAddress: 'Edit Address',
    deleteAddress: 'Delete Address',
    defaultAddress: 'Default Address',
    setAsDefault: 'Set as Default',
    addressTitle: 'Address Title',
    fullName: 'Full Name',
    company: 'Company',
    addressLine1: 'Address Line 1',
    addressLine2: 'Address Line 2',
    city: 'City',
    state: 'State/Province',
    postalCode: 'Postal Code',
    country: 'Country',
    addressSaved: 'Address saved',
    addressDeleted: 'Address deleted',
    confirmDeleteAddress: 'Are you sure you want to delete this address?',
  },
};
