import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL, API_TIMEOUT, STORAGE_KEYS } from '../constants';
import { ApiResponse } from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid, clear storage and redirect to login
          await AsyncStorage.multiRemove([
            STORAGE_KEYS.USER_TOKEN,
            STORAGE_KEYS.USER_DATA,
          ]);
          // You can dispatch a logout action here if using Redux
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.request<T>(config);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      console.error('API Request Error:', error);
      
      let errorMessage = 'An unexpected error occurred';
      
      if (error.response) {
        // Server responded with error status
        errorMessage = error.response.data?.message || error.response.statusText;
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection.';
      } else {
        // Other error
        errorMessage = error.message;
      }

      return {
        success: false,
        data: null as any,
        error: errorMessage,
      };
    }
  }

  // GET request
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'GET',
      url,
      params,
    });
  }

  // POST request
  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data,
    });
  }

  // PUT request
  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PUT',
      url,
      data,
    });
  }

  // DELETE request
  async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'DELETE',
      url,
    });
  }

  // PATCH request
  async patch<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'PATCH',
      url,
      data,
    });
  }

  // Upload file
  async upload<T>(url: string, formData: FormData): Promise<ApiResponse<T>> {
    return this.request<T>({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Set auth token
  setAuthToken(token: string) {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  // Remove auth token
  removeAuthToken() {
    delete this.api.defaults.headers.common['Authorization'];
  }

  // Get base URL
  getBaseUrl(): string {
    return API_BASE_URL;
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// OpenCart specific API methods
export class OpenCartApiService {
  private api = apiService;

  // Authentication
  async login(email: string, password: string) {
    return this.api.post('/login', { email, password });
  }

  async register(userData: any) {
    return this.api.post('/register', userData);
  }

  async logout() {
    return this.api.post('/logout');
  }

  async forgotPassword(email: string) {
    return this.api.post('/forgot-password', { email });
  }

  // Products
  async getProducts(params?: any) {
    return this.api.get('/products', params);
  }

  async getProduct(id: string) {
    return this.api.get(`/products/${id}`);
  }

  async searchProducts(query: string, filters?: any) {
    return this.api.get('/search', { query, ...filters });
  }

  async getFeaturedProducts() {
    return this.api.get('/products/featured');
  }

  async getProductsByCategory(categoryId: string, params?: any) {
    return this.api.get(`/categories/${categoryId}/products`, params);
  }

  // Categories
  async getCategories() {
    return this.api.get('/categories');
  }

  async getCategory(id: string) {
    return this.api.get(`/categories/${id}`);
  }

  // Cart
  async getCart() {
    return this.api.get('/cart');
  }

  async addToCart(productId: string, quantity: number, options?: any) {
    return this.api.post('/cart/add', {
      product_id: productId,
      quantity,
      options,
    });
  }

  async updateCartItem(itemId: string, quantity: number) {
    return this.api.put(`/cart/items/${itemId}`, { quantity });
  }

  async removeFromCart(itemId: string) {
    return this.api.delete(`/cart/items/${itemId}`);
  }

  async clearCart() {
    return this.api.delete('/cart');
  }

  // Orders
  async getOrders(params?: any) {
    return this.api.get('/orders', params);
  }

  async getOrder(id: string) {
    return this.api.get(`/orders/${id}`);
  }

  async createOrder(orderData: any) {
    return this.api.post('/orders', orderData);
  }

  // User Profile
  async getProfile() {
    return this.api.get('/profile');
  }

  async updateProfile(profileData: any) {
    return this.api.put('/profile', profileData);
  }

  async getAddresses() {
    return this.api.get('/addresses');
  }

  async addAddress(addressData: any) {
    return this.api.post('/addresses', addressData);
  }

  async updateAddress(id: string, addressData: any) {
    return this.api.put(`/addresses/${id}`, addressData);
  }

  async deleteAddress(id: string) {
    return this.api.delete(`/addresses/${id}`);
  }

  // Reviews
  async getProductReviews(productId: string, params?: any) {
    return this.api.get(`/products/${productId}/reviews`, params);
  }

  async addReview(productId: string, reviewData: any) {
    return this.api.post(`/products/${productId}/reviews`, reviewData);
  }

  // Wishlist
  async getWishlist() {
    return this.api.get('/wishlist');
  }

  async addToWishlist(productId: string) {
    return this.api.post('/wishlist', { product_id: productId });
  }

  async removeFromWishlist(productId: string) {
    return this.api.delete(`/wishlist/${productId}`);
  }

  // Payment
  async initializePayment(paymentData: any) {
    return this.api.post('/payment/initialize', paymentData);
  }

  async verifyPayment(paymentId: string) {
    return this.api.get(`/payment/verify/${paymentId}`);
  }

  // Contact
  async sendContactMessage(messageData: any) {
    return this.api.post('/contact', messageData);
  }

  // Newsletter
  async subscribeNewsletter(email: string) {
    return this.api.post('/newsletter/subscribe', { email });
  }

  async unsubscribeNewsletter(email: string) {
    return this.api.post('/newsletter/unsubscribe', { email });
  }
}

export const openCartApi = new OpenCartApiService();
