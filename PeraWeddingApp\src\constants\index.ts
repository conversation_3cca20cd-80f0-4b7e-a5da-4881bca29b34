// App Constants
export const APP_NAME = 'Pera Wedding';
export const APP_VERSION = '1.0.0';

// API Configuration
export const API_BASE_URL = 'https://www.perawedding.com.tr/api'; // OpenCart API endpoint
export const API_TIMEOUT = 10000;

// Colors from the website
export const COLORS = {
  primary: '#bca89a',
  secondary: '#201c1d',
  background: '#ffffff',
  surface: '#f3f3f3',
  text: '#201c1d',
  textSecondary: '#808080',
  accent: '#bca89a',
  error: '#ff4444',
  success: '#00C851',
  warning: '#ffbb33',
  info: '#33b5e5',
  white: '#ffffff',
  black: '#000000',
  gray: '#808080',
  lightGray: '#f3f3f3',
  darkGray: '#332832',
};

// Typography
export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

export const FONT_SIZES = {
  xs: 11,
  sm: 12,
  md: 13,
  lg: 14,
  xl: 16,
  xxl: 17,
  xxxl: 20,
  huge: 35,
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Screen dimensions
export const SCREEN_WIDTH = 375; // Default width for calculations
export const SCREEN_HEIGHT = 812; // Default height for calculations

// Product categories (from website analysis)
export const PRODUCT_CATEGORIES = {
  WEDDING_MEMORY_BOOKS: 'wedding-memory-books',
  BABY_MEMORY_BOOKS: 'baby-memory-books',
  DIGITAL_INVITATIONS: 'digital-invitations',
  WEDDING_PLANNER: 'wedding-planner',
};

// PayTR Configuration
export const PAYTR_CONFIG = {
  MERCHANT_ID: '', // To be configured
  MERCHANT_KEY: '', // To be configured
  MERCHANT_SALT: '', // To be configured
  TEST_MODE: true,
  CURRENCY: 'TL',
  LANG: 'tr',
};

// Storage Keys
export const STORAGE_KEYS = {
  USER_TOKEN: '@user_token',
  USER_DATA: '@user_data',
  CART_DATA: '@cart_data',
  LANGUAGE: '@language',
  GUEST_CART: '@guest_cart',
};

// Navigation Routes
export const ROUTES = {
  // Auth Stack
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',
  
  // Main Stack
  HOME: 'Home',
  CATEGORIES: 'Categories',
  PRODUCTS: 'Products',
  PRODUCT_DETAIL: 'ProductDetail',
  CART: 'Cart',
  CHECKOUT: 'Checkout',
  PAYMENT: 'Payment',
  
  // Account Stack
  PROFILE: 'Profile',
  ORDER_HISTORY: 'OrderHistory',
  ORDER_DETAIL: 'OrderDetail',
  ADDRESSES: 'Addresses',
  SETTINGS: 'Settings',
  
  // Other
  SEARCH: 'Search',
  WISHLIST: 'Wishlist',
  CONTACT: 'Contact',
  ABOUT: 'About',
};

// API Endpoints
export const API_ENDPOINTS = {
  // Auth
  LOGIN: '/login',
  REGISTER: '/register',
  LOGOUT: '/logout',
  FORGOT_PASSWORD: '/forgot-password',
  
  // Products
  PRODUCTS: '/products',
  CATEGORIES: '/categories',
  PRODUCT_DETAIL: '/products/{id}',
  SEARCH: '/search',
  
  // Cart
  CART: '/cart',
  ADD_TO_CART: '/cart/add',
  UPDATE_CART: '/cart/update',
  REMOVE_FROM_CART: '/cart/remove',
  
  // Orders
  ORDERS: '/orders',
  ORDER_DETAIL: '/orders/{id}',
  CREATE_ORDER: '/orders/create',
  
  // User
  PROFILE: '/profile',
  UPDATE_PROFILE: '/profile/update',
  ADDRESSES: '/addresses',
  
  // Payment
  PAYMENT_INIT: '/payment/init',
  PAYMENT_CALLBACK: '/payment/callback',
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Ağ bağlantısı hatası',
  INVALID_CREDENTIALS: 'Geçersiz kullanıcı adı veya şifre',
  REQUIRED_FIELD: 'Bu alan zorunludur',
  INVALID_EMAIL: 'Geçersiz e-posta adresi',
  PASSWORD_TOO_SHORT: 'Şifre en az 6 karakter olmalıdır',
  GENERIC_ERROR: 'Bir hata oluştu, lütfen tekrar deneyin',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Başarıyla giriş yapıldı',
  REGISTER_SUCCESS: 'Hesabınız başarıyla oluşturuldu',
  PRODUCT_ADDED_TO_CART: 'Ürün sepete eklendi',
  ORDER_PLACED: 'Siparişiniz başarıyla oluşturuldu',
  PROFILE_UPDATED: 'Profiliniz güncellendi',
};

// Validation Rules
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^[0-9]{10,11}$/,
  MIN_PASSWORD_LENGTH: 6,
  MAX_PASSWORD_LENGTH: 50,
};

// Image Placeholders
export const PLACEHOLDER_IMAGES = {
  PRODUCT: 'https://via.placeholder.com/300x300?text=Product',
  USER_AVATAR: 'https://via.placeholder.com/100x100?text=User',
  CATEGORY: 'https://via.placeholder.com/200x200?text=Category',
};

// Animation Durations
export const ANIMATION_DURATION = {
  SHORT: 200,
  MEDIUM: 300,
  LONG: 500,
};

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
};
