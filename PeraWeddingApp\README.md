# Pera Wedding Mobile App

A React Native e-commerce application that clones the perawedding.com.tr website, featuring wedding memory books and related products with PayTR payment integration.

## Features

- 🛍️ Complete e-commerce functionality
- 🔐 User authentication (login, register, forgot password)
- 📱 Responsive mobile design matching the website
- 🛒 Shopping cart and checkout system
- 💳 PayTR payment integration
- 🌍 Multilingual support (Turkish & English)
- 📦 Order management and tracking
- ❤️ Wishlist functionality
- 🔍 Product search and filtering
- 📱 Push notifications
- 🎨 Professional UI/UX design

## Tech Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **State Management**: Redux Toolkit
- **Navigation**: React Navigation v6
- **UI Components**: React Native Elements, Expo Vector Icons
- **API Integration**: Axios
- **Payment**: PayTR integration
- **Storage**: AsyncStorage
- **Internationalization**: i18n-js
- **Notifications**: Expo Notifications

## Project Structure

```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
│   ├── auth/          # Authentication screens
│   └── main/          # Main app screens
├── navigation/         # Navigation configuration
├── store/             # Redux store and slices
├── services/          # API and external services
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
├── constants/         # App constants
├── hooks/             # Custom React hooks
├── locales/           # Internationalization files
└── assets/            # Images, fonts, etc.
```

## Installation

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PeraWeddingApp
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Configure environment variables**
   
   Create a `.env` file in the root directory:
   ```env
   # API Configuration
   API_BASE_URL=https://www.perawedding.com.tr/api
   
   # PayTR Configuration
   PAYTR_MERCHANT_ID=your_merchant_id
   PAYTR_MERCHANT_KEY=your_merchant_key
   PAYTR_MERCHANT_SALT=your_merchant_salt
   PAYTR_TEST_MODE=true
   ```

4. **Update PayTR configuration**
   
   Edit `src/constants/index.ts` and update the PayTR configuration:
   ```typescript
   export const PAYTR_CONFIG = {
     MERCHANT_ID: 'your_merchant_id',
     MERCHANT_KEY: 'your_merchant_key',
     MERCHANT_SALT: 'your_merchant_salt',
     TEST_MODE: true, // Set to false for production
     CURRENCY: 'TL',
     LANG: 'tr',
   };
   ```

5. **Configure API endpoints**
   
   Update the API base URL in `src/constants/index.ts` to match your OpenCart API:
   ```typescript
   export const API_BASE_URL = 'https://www.perawedding.com.tr/api';
   ```

## Development

### Running the app

1. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

2. **Run on Android**
   ```bash
   npm run android
   # or
   yarn android
   ```

3. **Run on iOS** (macOS only)
   ```bash
   npm run ios
   # or
   yarn ios
   ```

### Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## API Integration

The app is designed to work with OpenCart API. You'll need to:

1. **Install OpenCart REST API extension** on your OpenCart store
2. **Configure API credentials** in your OpenCart admin panel
3. **Update API endpoints** in the app to match your store's API structure

### Required API Endpoints

- Authentication: `/login`, `/register`, `/logout`
- Products: `/products`, `/products/{id}`, `/search`
- Categories: `/categories`
- Cart: `/cart`, `/cart/add`, `/cart/update`, `/cart/remove`
- Orders: `/orders`, `/orders/{id}`, `/orders/create`
- User: `/profile`, `/addresses`
- Payment: `/payment/initialize`, `/payment/verify`

## PayTR Integration

### Setup PayTR Account

1. **Register** at [PayTR](https://www.paytr.com/)
2. **Get your credentials**: Merchant ID, Merchant Key, and Merchant Salt
3. **Configure webhook URLs** in PayTR panel:
   - Success URL: `your-domain.com/payment/success`
   - Fail URL: `your-domain.com/payment/fail`

### Test Mode

The app is configured to use PayTR test mode by default. To enable production mode:

1. Set `TEST_MODE: false` in PayTR configuration
2. Use production credentials
3. Test thoroughly before going live

## Building for Production

### Android APK

1. **Configure app signing**
   ```bash
   # Generate keystore
   keytool -genkeypair -v -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **Build APK**
   ```bash
   # Build for production
   expo build:android
   
   # Or build locally
   eas build --platform android
   ```

### iOS App Store

1. **Configure iOS certificates** in Apple Developer Console
2. **Build for iOS**
   ```bash
   expo build:ios
   
   # Or build locally
   eas build --platform ios
   ```

## Deployment

### Google Play Store

1. **Prepare app for release**
   - Update version in `app.json`
   - Generate signed APK
   - Test on multiple devices

2. **Upload to Play Console**
   - Create app listing
   - Upload APK/AAB
   - Configure store listing
   - Submit for review

### Apple App Store

1. **Prepare app for release**
   - Update version in `app.json`
   - Generate iOS build
   - Test on multiple devices

2. **Upload to App Store Connect**
   - Create app record
   - Upload build via Xcode or Transporter
   - Configure app information
   - Submit for review

## Configuration

### App Configuration

Edit `app.json` to customize app settings:

```json
{
  "expo": {
    "name": "Pera Wedding",
    "slug": "pera-wedding-app",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#bca89a"
    },
    "updates": {
      "fallbackToCacheTimeout": 0
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.perawedding.app"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#bca89a"
      },
      "package": "com.perawedding.app"
    }
  }
}
```

### Push Notifications

Configure push notifications in `app.json`:

```json
{
  "expo": {
    "notification": {
      "icon": "./assets/notification-icon.png",
      "color": "#bca89a"
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **Metro bundler issues**
   ```bash
   npx react-native start --reset-cache
   ```

2. **Android build issues**
   ```bash
   cd android && ./gradlew clean && cd ..
   ```

3. **iOS build issues**
   ```bash
   cd ios && rm -rf Pods && pod install && cd ..
   ```

4. **PayTR integration issues**
   - Verify credentials are correct
   - Check webhook URLs
   - Ensure test mode is properly configured

### Performance Optimization

- Use `React.memo` for expensive components
- Implement lazy loading for images
- Use FlatList for large lists
- Optimize bundle size with code splitting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Website: https://www.perawedding.com.tr
- Phone: +90 XXX XXX XX XX

## Changelog

### Version 1.0.0
- Initial release
- Complete e-commerce functionality
- PayTR payment integration
- Multilingual support
- Push notifications
- Order management
