{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "expo r -c", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/drawer": "^7.5.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.5", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.11.0", "expo": "~53.0.20", "expo-localization": "^16.1.6", "expo-notifications": "^0.31.4", "expo-status-bar": "~2.2.3", "i18n-js": "^4.5.1", "node-paytr": "^1.1.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.3.0", "react-native-webview": "^13.15.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}