// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female';
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  images: ProductImage[];
  category: Category;
  subcategory?: Category;
  brand?: string;
  sku: string;
  stock: number;
  isInStock: boolean;
  rating: number;
  reviewCount: number;
  tags: string[];
  options: ProductOption[];
  specifications: ProductSpecification[];
  createdAt: string;
  updatedAt: string;
}

export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
}

export interface ProductOption {
  id: string;
  name: string;
  type: 'select' | 'radio' | 'checkbox' | 'text';
  required: boolean;
  values: ProductOptionValue[];
}

export interface ProductOptionValue {
  id: string;
  name: string;
  value: string;
  priceModifier?: number;
  imageUrl?: string;
}

export interface ProductSpecification {
  name: string;
  value: string;
}

// Category Types
export interface Category {
  id: string;
  name: string;
  description?: string;
  image?: string;
  parentId?: string;
  children?: Category[];
  productCount: number;
  order: number;
  isActive: boolean;
}

// Cart Types
export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  selectedOptions: SelectedOption[];
  totalPrice: number;
  addedAt: string;
}

export interface SelectedOption {
  optionId: string;
  optionName: string;
  valueId: string;
  valueName: string;
  priceModifier?: number;
}

export interface Cart {
  items: CartItem[];
  totalItems: number;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
}

// Order Types
export interface Order {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  shippingAddress: Address;
  billingAddress: Address;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
  trackingNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  selectedOptions: SelectedOption[];
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export type PaymentStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'refunded';

// Address Types
export interface Address {
  id: string;
  type: 'shipping' | 'billing';
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
}

// Payment Types
export interface PaymentMethod {
  id: string;
  name: string;
  type: 'paytr' | 'credit_card' | 'bank_transfer';
  isActive: boolean;
}

export interface PayTRPayment {
  merchantId: string;
  userIp: string;
  merchantOid: string;
  email: string;
  paymentAmount: number;
  currency: string;
  testMode: boolean;
  userBasket: string;
  userAddress: string;
  userPhone: string;
  merchantOkUrl: string;
  merchantFailUrl: string;
  paytrToken: string;
}

// Search and Filter Types
export interface SearchFilters {
  query?: string;
  categoryId?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  sortBy?: 'name' | 'price' | 'rating' | 'newest';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchResult {
  products: Product[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  ProductDetail: { productId: string };
  Search: { query?: string };
  Category: { categoryId: string; categoryName: string };
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Categories: undefined;
  Cart: undefined;
  Profile: undefined;
  Search: undefined;
};

// Form Types
export interface LoginForm {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  acceptTerms: boolean;
}

export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

// Notification Types
export interface PushNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  type: 'order_update' | 'promotion' | 'general';
  createdAt: string;
  isRead: boolean;
}

// Language Types
export type Language = 'tr' | 'en';

export interface LanguageOption {
  code: Language;
  name: string;
  flag: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Review Types
export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  productId: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  isVerifiedPurchase: boolean;
  helpfulCount: number;
  createdAt: string;
  updatedAt: string;
}

// Wishlist Types
export interface WishlistItem {
  id: string;
  product: Product;
  addedAt: string;
}

// App State Types
export interface AppState {
  auth: AuthState;
  products: ProductState;
  cart: CartState;
  orders: OrderState;
  categories: CategoryState;
  wishlist: WishlistState;
  app: AppGlobalState;
}

export interface ProductState extends LoadingState {
  products: Product[];
  featuredProducts: Product[];
  searchResults: SearchResult | null;
  currentProduct: Product | null;
  filters: SearchFilters;
}

export interface CartState extends LoadingState {
  cart: Cart;
  isUpdating: boolean;
}

export interface OrderState extends LoadingState {
  orders: Order[];
  currentOrder: Order | null;
}

export interface CategoryState extends LoadingState {
  categories: Category[];
  currentCategory: Category | null;
}

export interface WishlistState extends LoadingState {
  items: WishlistItem[];
}

export interface AppGlobalState {
  language: Language;
  isFirstLaunch: boolean;
  notifications: PushNotification[];
  isConnected: boolean;
}
