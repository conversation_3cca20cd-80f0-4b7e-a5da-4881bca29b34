# Pera Wedding App - API Documentation

This document outlines the API endpoints required for the Pera Wedding mobile app to function properly with the OpenCart backend.

## Base URL

```
https://www.perawedding.com.tr/api
```

## Authentication

The API uses Bearer token authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-token>
```

## API Endpoints

### Authentication

#### POST /auth/login
Login user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "1",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "phone": "+90 ************",
      "createdAt": "2023-01-01T00:00:00Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### POST /auth/register
Register new user account.

**Request Body:**
```json
{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+90 ************"
}
```

#### POST /auth/logout
Logout current user.

#### POST /auth/forgot-password
Send password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### Products

#### GET /products
Get list of products with pagination and filtering.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `category_id` (optional): Filter by category
- `search` (optional): Search query
- `sort` (optional): Sort by (name, price, rating, newest)
- `order` (optional): Sort order (asc, desc)
- `min_price` (optional): Minimum price filter
- `max_price` (optional): Maximum price filter

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "1",
        "name": "Düğün Anı Defteri - Premium",
        "description": "Özel tasarım düğün anı defteri...",
        "price": 299.99,
        "originalPrice": 399.99,
        "images": [
          {
            "id": "1",
            "url": "https://example.com/image1.jpg",
            "alt": "Product image",
            "isPrimary": true
          }
        ],
        "category": {
          "id": "1",
          "name": "Düğün Anı Defteri"
        },
        "rating": 4.5,
        "reviewCount": 25,
        "stock": 10,
        "isInStock": true
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 100,
      "itemsPerPage": 20
    }
  }
}
```

#### GET /products/{id}
Get single product details.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "1",
    "name": "Düğün Anı Defteri - Premium",
    "description": "Detailed product description...",
    "shortDescription": "Short description...",
    "price": 299.99,
    "originalPrice": 399.99,
    "images": [...],
    "category": {...},
    "options": [
      {
        "id": "1",
        "name": "Renk",
        "type": "select",
        "required": true,
        "values": [
          {
            "id": "1",
            "name": "Beyaz",
            "value": "white",
            "priceModifier": 0
          }
        ]
      }
    ],
    "specifications": [
      {
        "name": "Boyut",
        "value": "21x29.7 cm"
      }
    ],
    "reviews": [...],
    "relatedProducts": [...]
  }
}
```

#### GET /products/featured
Get featured products.

#### GET /search
Search products.

**Query Parameters:**
- `q`: Search query
- `category_id` (optional): Filter by category
- Other filtering parameters same as /products

### Categories

#### GET /categories
Get all categories with hierarchy.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "Düğün Anı Defteri",
      "description": "Düğün anılarınız için özel defterler",
      "image": "https://example.com/category1.jpg",
      "productCount": 25,
      "children": [
        {
          "id": "2",
          "name": "Premium Serisi",
          "productCount": 10
        }
      ]
    }
  ]
}
```

#### GET /categories/{id}
Get single category with products.

### Shopping Cart

#### GET /cart
Get current user's cart.

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "1",
        "product": {...},
        "quantity": 2,
        "selectedOptions": [
          {
            "optionId": "1",
            "optionName": "Renk",
            "valueId": "1",
            "valueName": "Beyaz"
          }
        ],
        "totalPrice": 599.98
      }
    ],
    "totalItems": 2,
    "subtotal": 599.98,
    "tax": 107.99,
    "shipping": 0,
    "total": 707.97,
    "currency": "TL"
  }
}
```

#### POST /cart/add
Add item to cart.

**Request Body:**
```json
{
  "product_id": "1",
  "quantity": 1,
  "options": {
    "1": "1"
  }
}
```

#### PUT /cart/items/{item_id}
Update cart item quantity.

**Request Body:**
```json
{
  "quantity": 3
}
```

#### DELETE /cart/items/{item_id}
Remove item from cart.

#### DELETE /cart
Clear entire cart.

### Orders

#### GET /orders
Get user's order history.

**Query Parameters:**
- `page` (optional): Page number
- `limit` (optional): Items per page
- `status` (optional): Filter by status

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "1",
        "orderNumber": "PW202301001",
        "status": "delivered",
        "items": [...],
        "total": 707.97,
        "currency": "TL",
        "createdAt": "2023-01-01T00:00:00Z",
        "shippingAddress": {...},
        "trackingNumber": "1234567890"
      }
    ],
    "pagination": {...}
  }
}
```

#### GET /orders/{id}
Get single order details.

#### POST /orders
Create new order.

**Request Body:**
```json
{
  "items": [
    {
      "product_id": "1",
      "quantity": 1,
      "options": {...}
    }
  ],
  "shippingAddress": {
    "firstName": "John",
    "lastName": "Doe",
    "address1": "123 Main St",
    "city": "Istanbul",
    "postalCode": "34000",
    "country": "Turkey",
    "phone": "+90 ************"
  },
  "billingAddress": {...},
  "paymentMethod": "paytr"
}
```

### User Profile

#### GET /profile
Get user profile information.

#### PUT /profile
Update user profile.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+90 ************",
  "dateOfBirth": "1990-01-01"
}
```

#### GET /addresses
Get user's saved addresses.

#### POST /addresses
Add new address.

#### PUT /addresses/{id}
Update address.

#### DELETE /addresses/{id}
Delete address.

### Wishlist

#### GET /wishlist
Get user's wishlist.

#### POST /wishlist
Add product to wishlist.

**Request Body:**
```json
{
  "product_id": "1"
}
```

#### DELETE /wishlist/{product_id}
Remove product from wishlist.

### Reviews

#### GET /products/{id}/reviews
Get product reviews.

#### POST /products/{id}/reviews
Add product review.

**Request Body:**
```json
{
  "rating": 5,
  "title": "Great product!",
  "comment": "I love this product...",
  "images": ["image1.jpg", "image2.jpg"]
}
```

### Payment

#### POST /payment/initialize
Initialize PayTR payment.

**Request Body:**
```json
{
  "order_id": "1",
  "amount": 707.97,
  "currency": "TL",
  "success_url": "app://payment/success",
  "fail_url": "app://payment/fail"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_url": "https://www.paytr.com/odeme/...",
    "payment_token": "abc123...",
    "merchant_oid": "PW202301001"
  }
}
```

#### POST /payment/callback
PayTR payment callback (webhook).

#### GET /payment/verify/{payment_id}
Verify payment status.

### Contact

#### POST /contact
Send contact message.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+90 ************",
  "subject": "Question about product",
  "message": "I have a question..."
}
```

### Newsletter

#### POST /newsletter/subscribe
Subscribe to newsletter.

#### POST /newsletter/unsubscribe
Unsubscribe from newsletter.

## Error Responses

All endpoints return errors in the following format:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Common Error Codes

- `UNAUTHORIZED` (401): Invalid or missing authentication token
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `VALIDATION_ERROR` (422): Invalid request data
- `INTERNAL_ERROR` (500): Server error

## Rate Limiting

API requests are limited to:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated users

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (starts from 1)
- `limit`: Items per page (max 100)

Pagination info is included in the response:
```json
{
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 200,
    "itemsPerPage": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

## Webhooks

### PayTR Payment Webhook

PayTR sends payment notifications to:
```
POST /webhooks/paytr
```

The webhook includes payment status and order information for processing completed payments.
