import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppGlobalState, Language, PushNotification } from '../../types';
import { STORAGE_KEYS } from '../../constants';

const initialState: AppGlobalState = {
  language: 'tr',
  isFirstLaunch: true,
  notifications: [],
  isConnected: true,
};

export const loadAppSettings = createAsyncThunk(
  'app/loadSettings',
  async (_, { rejectWithValue }) => {
    try {
      const language = await AsyncStorage.getItem(STORAGE_KEYS.LANGUAGE);
      return {
        language: (language as Language) || 'tr',
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const setLanguage = createAsyncThunk(
  'app/setLanguage',
  async (language: Language, { rejectWithValue }) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
      return language;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    addNotification: (state, action: PayloadAction<PushNotification>) => {
      state.notifications.unshift(action.payload);
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.isRead = true;
      }
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    setFirstLaunch: (state, action: PayloadAction<boolean>) => {
      state.isFirstLaunch = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadAppSettings.fulfilled, (state, action) => {
        state.language = action.payload.language;
      })
      .addCase(setLanguage.fulfilled, (state, action) => {
        state.language = action.payload;
      });
  },
});

export const {
  setConnectionStatus,
  addNotification,
  markNotificationAsRead,
  clearNotifications,
  setFirstLaunch,
} = appSlice.actions;

export default appSlice.reducer;
