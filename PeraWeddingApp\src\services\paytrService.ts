import { PayTRPayment } from '../types';
import { PAYTR_CONFIG } from '../constants';
import { generateOrderNumber } from '../utils';

export class PayTRService {
  private merchantId: string;
  private merchantKey: string;
  private merchantSalt: string;
  private testMode: boolean;

  constructor() {
    this.merchantId = PAYTR_CONFIG.MERCHANT_ID;
    this.merchantKey = PAYTR_CONFIG.MERCHANT_KEY;
    this.merchantSalt = PAYTR_CONFIG.MERCHANT_SALT;
    this.testMode = PAYTR_CONFIG.TEST_MODE;
  }

  // Initialize payment
  async initializePayment(paymentData: {
    amount: number;
    userEmail: string;
    userPhone: string;
    userAddress: string;
    userBasket: string;
    userIp: string;
    successUrl: string;
    failUrl: string;
  }): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const merchantOid = generateOrderNumber();
      
      // Create payment request
      const paymentRequest: PayTRPayment = {
        merchantId: this.merchantId,
        userIp: paymentData.userIp,
        merchantOid,
        email: paymentData.userEmail,
        paymentAmount: Math.round(paymentData.amount * 100), // PayTR expects amount in kuruş
        currency: PAYTR_CONFIG.CURRENCY,
        testMode: this.testMode,
        userBasket: paymentData.userBasket,
        userAddress: paymentData.userAddress,
        userPhone: paymentData.userPhone,
        merchantOkUrl: paymentData.successUrl,
        merchantFailUrl: paymentData.failUrl,
        paytrToken: '', // Will be generated
      };

      // Generate PayTR token
      const token = this.generatePayTRToken(paymentRequest);
      paymentRequest.paytrToken = token;

      // Create form data for PayTR
      const formData = this.createPayTRFormData(paymentRequest);

      return {
        success: true,
        data: {
          formData,
          paymentUrl: this.getPayTRUrl(),
          merchantOid,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Payment initialization failed',
      };
    }
  }

  // Generate PayTR token
  private generatePayTRToken(paymentData: PayTRPayment): string {
    const crypto = require('crypto');
    
    const hashStr = [
      this.merchantId,
      paymentData.userIp,
      paymentData.merchantOid,
      paymentData.email,
      paymentData.paymentAmount,
      paymentData.userBasket,
      paymentData.testMode ? 1 : 0,
      this.merchantSalt,
    ].join('');

    return crypto.createHmac('sha256', this.merchantKey).update(hashStr).digest('base64');
  }

  // Create form data for PayTR
  private createPayTRFormData(paymentData: PayTRPayment): Record<string, string> {
    return {
      merchant_id: paymentData.merchantId,
      user_ip: paymentData.userIp,
      merchant_oid: paymentData.merchantOid,
      email: paymentData.email,
      payment_amount: paymentData.paymentAmount.toString(),
      paytr_token: paymentData.paytrToken,
      user_basket: paymentData.userBasket,
      debug_on: this.testMode ? '1' : '0',
      no_installment: '0',
      max_installment: '0',
      user_name: paymentData.email,
      user_address: paymentData.userAddress,
      user_phone: paymentData.userPhone,
      merchant_ok_url: paymentData.merchantOkUrl,
      merchant_fail_url: paymentData.merchantFailUrl,
      timeout_limit: '30',
      currency: paymentData.currency,
      test_mode: this.testMode ? '1' : '0',
    };
  }

  // Get PayTR payment URL
  private getPayTRUrl(): string {
    return this.testMode 
      ? 'https://www.paytr.com/odeme/guvenli'
      : 'https://www.paytr.com/odeme';
  }

  // Verify payment callback
  async verifyPaymentCallback(callbackData: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const {
        merchant_oid,
        status,
        total_amount,
        hash,
      } = callbackData;

      // Verify hash
      const expectedHash = this.generateCallbackHash(merchant_oid, status, total_amount);
      
      if (hash !== expectedHash) {
        return {
          success: false,
          error: 'Invalid payment callback hash',
        };
      }

      // Check payment status
      if (status === 'success') {
        return {
          success: true,
          data: {
            merchantOid: merchant_oid,
            status,
            amount: total_amount,
            verified: true,
          },
        };
      } else {
        return {
          success: false,
          error: 'Payment failed or cancelled',
        };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Payment verification failed',
      };
    }
  }

  // Generate callback hash for verification
  private generateCallbackHash(merchantOid: string, status: string, totalAmount: string): string {
    const crypto = require('crypto');
    
    const hashStr = [
      merchantOid,
      this.merchantSalt,
      status,
      totalAmount,
    ].join('');

    return crypto.createHmac('sha256', this.merchantKey).update(hashStr).digest('base64');
  }

  // Create user basket string for PayTR
  createUserBasket(cartItems: any[]): string {
    const basket = cartItems.map(item => [
      item.product.name,
      item.product.price.toFixed(2),
      item.quantity,
    ]);

    return JSON.stringify(basket);
  }

  // Get user IP (you'll need to implement this based on your backend)
  async getUserIP(): Promise<string> {
    try {
      // This should be implemented to get user's real IP
      // For now, return a placeholder
      return '127.0.0.1';
    } catch {
      return '127.0.0.1';
    }
  }

  // Validate payment amount
  validatePaymentAmount(amount: number): boolean {
    return amount > 0 && amount <= 999999.99;
  }

  // Format amount for PayTR (convert to kuruş)
  formatAmountForPayTR(amount: number): number {
    return Math.round(amount * 100);
  }

  // Format amount from PayTR (convert from kuruş)
  formatAmountFromPayTR(amount: number): number {
    return amount / 100;
  }

  // Check if PayTR is configured
  isConfigured(): boolean {
    return !!(this.merchantId && this.merchantKey && this.merchantSalt);
  }

  // Set configuration
  setConfiguration(config: {
    merchantId: string;
    merchantKey: string;
    merchantSalt: string;
    testMode?: boolean;
  }) {
    this.merchantId = config.merchantId;
    this.merchantKey = config.merchantKey;
    this.merchantSalt = config.merchantSalt;
    this.testMode = config.testMode ?? this.testMode;
  }

  // Get configuration status
  getConfigurationStatus(): {
    isConfigured: boolean;
    testMode: boolean;
    merchantId: string;
  } {
    return {
      isConfigured: this.isConfigured(),
      testMode: this.testMode,
      merchantId: this.merchantId,
    };
  }
}

// Create and export singleton instance
export const paytrService = new PayTRService();
